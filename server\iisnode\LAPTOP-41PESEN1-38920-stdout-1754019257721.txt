上传目录结构初始化完成
[2025-08-01 03:34:18] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-01 03:34:18] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-01 03:34:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 03:34:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 03:34:18] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":5038,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-01 03:34:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 03:34:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-01 03:34:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
